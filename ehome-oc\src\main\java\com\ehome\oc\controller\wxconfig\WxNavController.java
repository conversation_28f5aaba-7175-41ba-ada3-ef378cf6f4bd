package com.ehome.oc.controller.wxconfig;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;

import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.common.utils.uuid.Seq;
import com.jfinal.plugin.activerecord.Db;

import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@Controller
@RequestMapping("/oc/wxNav")
public class WxNavController extends BaseController {

    private static final String PREFIX = "oc/wx/config/nav";

    @RequestMapping("/wxNav")
    public String wxNav(ModelMap mmap) {
        mmap.put("source", "indexNav");
        mmap.put("isAdmin", getSysUser().isAdmin());
        return PREFIX+"/list";
    }
    @RequestMapping("/infoNav")
    public String infoNav(ModelMap mmap) {
        mmap.put("source", "infoNav");
        mmap.put("isAdmin", getSysUser().isAdmin());
        return PREFIX+"/list";
    }

    @GetMapping("/add")
    public String add(String source, String parentId, ModelMap mmap) {
        mmap.put("source", source);
        mmap.put("parentId", parentId);
        mmap.put("isAdmin", getSysUser().isAdmin());
        return PREFIX+"/add";
    }

    @GetMapping("/tree")
    public String tree(String source, ModelMap mmap) {
        mmap.put("source", source);
        return PREFIX+"/tree";
    }

    @GetMapping("/vant-icons")
    public String vantIcons() {
        return PREFIX+"/vant-icons";
    }

    @GetMapping("/edit/{navId}")
    public String edit(@PathVariable("navId") Long navId, ModelMap mmap)
    {
        Record navInfo = Db.findFirst("select * from eh_wx_nav where nav_id = ?", navId);
        Map<String, Object> navMap = navInfo.toMap();

        // 检查是否有子菜单
        int childCount = Db.queryInt("SELECT COUNT(*) FROM eh_wx_nav WHERE parent_id = ?", navId);
        navMap.put("hasChildren", childCount > 0);

        mmap.put("nav", navMap);
        mmap.put("navId", navId);
        mmap.put("isAdmin", getSysUser().isAdmin());
        return PREFIX + "/edit";
    }

    @GetMapping("/view/{navId}")
    public String view(@PathVariable("navId") Long navId,ModelMap mmap)
    {
        Record navInfo = Db.findFirst("select * from eh_wx_nav where nav_id = ?", navId);
        Map<String, Object> navMap = navInfo.toMap();

        // 确保nav_type有默认值
        String navType = navInfo.getStr("nav_type");
        if (navType == null) {
            navMap.put("nav_type", "text");
        }

        // 如果是PDF类型，解析PDF文件列表
        if ("pdf".equals(navType)) {
            String pdfFileList = navInfo.getStr("pdf_file_list");
            List<Map<String, Object>> fileList = new ArrayList<>();

            if (StringUtils.isNotEmpty(pdfFileList)) {
                try {
                    JSONArray jsonArray = JSONArray.parseArray(pdfFileList);
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject fileObj = jsonArray.getJSONObject(i);
                        Map<String, Object> fileMap = new HashMap<>();
                        fileMap.put("name", fileObj.getString("name"));
                        String fileId = fileObj.getString("id");
                        fileMap.put("url", fileObj.getString("url"));
                        fileMap.put("id", fileId);
                        fileMap.put("type", fileObj.getString("type"));
                        fileList.add(fileMap);
                    }
                } catch (Exception e) {
                    logger.error("解析PDF文件列表失败: " + e.getMessage(),e);
                }
            }
            mmap.put("pdfFileList", fileList);
        }

        mmap.put("nav", navMap);
        mmap.put("navId", navId);
        return PREFIX + "/view";
    }

    @RequestMapping("/record")
    @ResponseBody
    public AjaxResult record(){
        JSONObject params = getParams();
        String navId   = params.getString("nav_id");
        Record navInfo = Db.findFirst("select * from eh_wx_nav where nav_id = ?", navId);
        return success(navInfo.toMap());
    }

    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(){
        JSONObject params = getParams();

        // 验证菜单名称长度
        String navName = params.getString("nav_name");
        if (StringUtils.isEmpty(navName) || navName.length() < 2 || navName.length() > 8) {
            return AjaxResult.error("菜单名称必须是2-8个字符");
        }

        // 验证infoNav的nav_type限制
        String source = params.getString("source");
        String navType = params.getString("nav_type");
        if ("infoNav".equals(source) && !"text".equals(navType) && !"pdf".equals(navType)) {
            return AjaxResult.error("信息导航菜单只支持文本和PDF类型");
        }

        // 验证page类型
        if ("page".equals(navType)) {
            String tapName = params.getString("tap_name");
            if (StringUtils.isEmpty(tapName)) {
                return AjaxResult.error("请选择内置页面");
            }
        }

        Record record = new Record();
        record.setColumns(params);
        record.set("community_id", getSysUser().getCommunityId());
        record.set("create_time", DateUtils.getTime());
        record.set("update_time", DateUtils.getTime());
        record.set("update_by", getLoginName());
        record.set("nav_code", "other");
        record.set("is_default", 1);
        record.set("source", source);
        record.set("top_show", 0);

        // 处理parent_id
        String parentId = params.getString("parent_id");
        if (StringUtils.isNotEmpty(parentId)) {
            record.set("parent_id", Integer.parseInt(parentId));

            // 验证菜单层级（不能超过二级）
            AjaxResult levelCheck = validateMenuLevel(Integer.parseInt(parentId));
            if (!levelCheck.isSuccess()) {
                return levelCheck;
            }
        } else {
            record.set("parent_id", 0);
        }

        // 自动获取最大排序值+1（按来源、社区和父菜单分组）
        Integer maxSort = Db.queryInt("SELECT IFNULL(MAX(sort), 0) FROM eh_wx_nav WHERE community_id = ? AND source = ? AND parent_id = ?",
            getSysUser().getCommunityId(), source, record.getInt("parent_id"));
        record.set("sort", maxSort + 1);

        // 只有PDF类型才处理PDF文件列表
        if ("pdf".equals(navType)) {
            processPdfFileList(params, record);
        } else {
            // 非PDF类型，清空PDF相关字段，但保留用户设置的url字段
            record.set("pdf_file_list", "");
            record.set("pdf_file", "");
            record.set("pdf_file_id", "");
        }
        record.remove("pdfFileIds");
        boolean result = Db.save("eh_wx_nav", "nav_id", record);

        if (result) {
            // 获取新插入的nav_id
            Long navId = record.getLong("nav_id");
            // 保存附件关联
            String pdfFileIds = params.getString("pdfFileIds");
            if (StringUtils.isNotEmpty(pdfFileIds)) {
                String[] fileIds = pdfFileIds.split(",");
                try {
                    saveNavAttachments("nav", navId.toString(), fileIds, getLoginName(),getSysUser().getCommunityId());
                } catch (Exception e) {
                    logger.error("保存导航附件关联失败: " + e.getMessage(),e);
                }
            }
        }
        return result ? success() : error();
    }

    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult edit(){
        JSONObject params = getParams();

        // 验证菜单名称长度
        String navName = params.getString("nav_name");
        if (StringUtils.isEmpty(navName) || navName.length() < 2 || navName.length() > 8) {
            return AjaxResult.error("菜单名称必须是2-8个字符");
        }

        // 获取当前记录的source信息
        String navId = params.getString("nav_id");
        Record currentNav = Db.findFirst("select source from eh_wx_nav where nav_id = ?", navId);
        if (currentNav != null) {
            String source = currentNav.getStr("source");
            String navType = params.getString("nav_type");
            // 验证infoNav的nav_type限制
            if ("infoNav".equals(source) && !"text".equals(navType) && !"pdf".equals(navType)) {
                return AjaxResult.error("信息导航菜单只支持文本和PDF类型");
            }

            // 验证page类型
            if ("page".equals(navType)) {
                String tapName = params.getString("tap_name");
                if (StringUtils.isEmpty(tapName)) {
                    return AjaxResult.error("请选择内置页面");
                }
            }
        }

        Record record = new Record();
        record.setColumns(params);
        record.set("update_time", DateUtils.getTime());
        record.set("update_by", getLoginName());
        record.set("is_default", 1);

        // 处理parent_id
        String parentId = params.getString("parent_id");
        if (StringUtils.isNotEmpty(parentId)) {
            int newParentId = Integer.parseInt(parentId);
            record.set("parent_id", newParentId);

            // 验证菜单层级（不能超过二级）
            AjaxResult levelCheck = validateMenuLevel(newParentId);
            if (!levelCheck.isSuccess()) {
                return levelCheck;
            }

            // 验证不能将菜单设置为自己的子菜单
            String currentNavId = params.getString("nav_id");
            if (currentNavId.equals(parentId)) {
                return AjaxResult.error("不能将菜单设置为自己的子菜单");
            }
        } else {
            record.set("parent_id", 0);
        }

        // 只有PDF类型才处理PDF文件列表
        String navType = params.getString("nav_type");
        if ("pdf".equals(navType)) {
            processPdfFileList(params, record);
        } else {
            // 非PDF类型，清空PDF相关字段，但保留用户设置的url字段
            record.set("pdf_file_list", "");
            record.set("pdf_file", "");
            record.set("pdf_file_id", "");
        }
        record.remove("pdfFileIds");
        boolean result = Db.update("eh_wx_nav", "nav_id", record);
        if (result) {
            String pdfFileIds = params.getString("pdfFileIds");
            if (StringUtils.isNotEmpty(pdfFileIds)) {
                String[] fileIds = pdfFileIds.split(",");
                try {
                    saveNavAttachments("nav", navId, fileIds, getLoginName(),getSysUser().getCommunityId());
                } catch (Exception e) {
                    logger.error("保存导航附件关联失败: " + e.getMessage(),e);
                }
            }
        }
        return result ? success() : error();
    }

    @ResponseBody
    @PostMapping("/list")
    public List<Map<String, Object>> list(){
        JSONObject params = getParams();
        EasySQL sql = new EasySQL();
        sql.append("from eh_wx_nav t1");
        sql.append("where 1=1");
        sql.append(getSysUser().getCommunityId(),"and community_id = ?");
        sql.append(params.getString("status"),"and t1.status = ?");
        sql.append(params.getString("source"),"and t1.source = ?");
        sql.append("order by t1.parent_id, t1.top_show desc, t1.sort, t1.update_time desc");

        List<Record> records = Db.find("select t1.*" + sql.toFullSql());

        // 转换为Map列表，TreeTable组件会自动构建树形关系
        List<Map<String, Object>> result = new ArrayList<>();

        // 统计每个菜单的子菜单数量
        Map<Integer, Integer> childCountMap = new HashMap<>();
        for (Record record : records) {
            Integer parentId = record.getInt("parent_id");
            if (parentId != null && parentId != 0) {
                childCountMap.put(parentId, childCountMap.getOrDefault(parentId, 0) + 1);
            }
        }

        for (Record record : records) {
            Map<String, Object> map = record.toMap();
            Integer navId = record.getInt("nav_id");
            // 添加是否有子菜单的标识
            map.put("hasChildren", childCountMap.getOrDefault(navId, 0) > 0);
            result.add(map);
        }
        return result;
    }


    /**
     * 菜单状态修改
     */
    @PostMapping("/changeStatus")
    @ResponseBody
    public AjaxResult changeStatus(){
        JSONObject params = getParams();
        String navId = params.getString("nav_id");
        String status = params.getString("status");
        if (StringUtils.isEmpty(navId)) {
            return AjaxResult.error("菜单ID不能为空");
        }
        if (StringUtils.isEmpty(status)) {
            return AjaxResult.error("状态不能为空");
        }
        Record record = new Record();
        record.set("nav_id", navId);
        record.set("status", status);
        record.set("update_time", DateUtils.getTime());
        record.set("update_by", getLoginName());
        boolean result = Db.update("eh_wx_nav", "nav_id", record);
        return result ? AjaxResult.success() : AjaxResult.error("状态修改失败");
    }

    /**
     * 菜单首页显示修改
     */
    @PostMapping("/changeIndexShow")
    @ResponseBody
    public AjaxResult changeIndexShow(){
        JSONObject params = getParams();
        String navId = params.getString("nav_id");
        String indexShow = params.getString("index_show");
        if (StringUtils.isEmpty(navId)) {
            return AjaxResult.error("菜单ID不能为空");
        }
        if (StringUtils.isEmpty(indexShow)) {
            return AjaxResult.error("首页显示状态不能为空");
        }
        Record record = new Record();
        record.set("nav_id", navId);
        record.set("index_show", indexShow);
        record.set("update_time", DateUtils.getTime());
        record.set("update_by", getLoginName());
        boolean result = Db.update("eh_wx_nav", "nav_id", record);
        return result ? AjaxResult.success() : AjaxResult.error("首页显示状态修改失败");
    }

    /**
     * 菜单置顶显示修改
     */
    @PostMapping("/changeTopShow")
    @ResponseBody
    public AjaxResult changeTopShow(){
        JSONObject params = getParams();
        String navId = params.getString("nav_id");
        String topShow = params.getString("top_show");
        if (StringUtils.isEmpty(navId)) {
            return AjaxResult.error("菜单ID不能为空");
        }
        if (StringUtils.isEmpty(topShow)) {
            return AjaxResult.error("置顶状态不能为空");
        }
        Record record = new Record();
        record.set("nav_id", navId);
        record.set("top_show", topShow);
        record.set("update_time", DateUtils.getTime());
        record.set("update_by", getLoginName());
        boolean result = Db.update("eh_wx_nav", "nav_id", record);
        return result ? AjaxResult.success() : AjaxResult.error("置顶状态修改失败");
    }

    /**
     * 菜单默认类型修改（仅超级管理员）
     */
    @PostMapping("/changeIsDefault")
    @ResponseBody
    public AjaxResult changeIsDefault(){
        // 验证超级管理员权限
        if (!getSysUser().isAdmin()) {
            return AjaxResult.error("只有超级管理员才能修改菜单默认类型");
        }

        JSONObject params = getParams();
        String navId = params.getString("nav_id");
        String isDefault = params.getString("is_default");
        if (StringUtils.isEmpty(navId)) {
            return AjaxResult.error("菜单ID不能为空");
        }
        if (StringUtils.isEmpty(isDefault)) {
            return AjaxResult.error("默认类型不能为空");
        }
        Record record = new Record();
        record.set("nav_id", navId);
        record.set("is_default", isDefault);
        record.set("update_time", DateUtils.getTime());
        record.set("update_by", getLoginName());
        boolean result = Db.update("eh_wx_nav", "nav_id", record);
        return result ? AjaxResult.success() : AjaxResult.error("默认类型修改失败");
    }

    /**
     * 删除菜单
     */
    @RequestMapping("/remove")
    @ResponseBody
    public AjaxResult remove(){
        JSONObject params = getParams();
        String ids = params.getString("ids");
        if (StringUtils.isEmpty(ids)) {
            return AjaxResult.error("菜单ID不能为空");
        }

        String[] idArr = ids.split(",");
        for (String id : idArr) {
            // 检查是否有子菜单
            int childCount = Db.queryInt("SELECT COUNT(*) FROM eh_wx_nav WHERE parent_id = ?", id);
            if (childCount > 0) {
                Record nav = Db.findFirst("SELECT nav_name FROM eh_wx_nav WHERE nav_id = ?", id);
                String navName = nav != null ? nav.getStr("nav_name") : "未知菜单";
                return AjaxResult.error("菜单【" + navName + "】存在子菜单，不允许删除");
            }

            // 删除菜单
            Db.deleteById("eh_wx_nav", "nav_id", id);
        }
        return success();
    }

    /**
     * 菜单排序修改
     */
    @PostMapping("/updateSort")
    @ResponseBody
    public AjaxResult updateSort(){
        JSONObject params = getParams();
        String navId = params.getString("nav_id");
        String sort = params.getString("sort");
        if (StringUtils.isEmpty(navId)) {
            return AjaxResult.error("菜单ID不能为空");
        }
        if (StringUtils.isEmpty(sort)) {
            return AjaxResult.error("排序值不能为空");
        }
        try {
            Integer sortValue = Integer.parseInt(sort);
            Record record = new Record();
            record.set("nav_id", navId);
            record.set("sort", sortValue);
            record.set("update_time", DateUtils.getTime());
            record.set("update_by", getLoginName());
            boolean result = Db.update("eh_wx_nav", "nav_id", record);
            return result ? AjaxResult.success() : AjaxResult.error("排序修改失败");
        } catch (NumberFormatException e) {
            return AjaxResult.error("排序值必须为数字");
        }
    }

    /**
     * 批量菜单排序修改
     */
    @PostMapping("/updateBatchSort")
    @ResponseBody
    public AjaxResult updateBatchSort(){
        JSONObject params = getParams();
        String navIds = params.getString("navIds");
        String sorts = params.getString("sorts");
        if (StringUtils.isEmpty(navIds)) {
            return AjaxResult.error("菜单ID不能为空");
        }
        if (StringUtils.isEmpty(sorts)) {
            return AjaxResult.error("排序值不能为空");
        }

        String[] navIdArr = navIds.split(",");
        String[] sortArr = sorts.split(",");

        if (navIdArr.length != sortArr.length) {
            return AjaxResult.error("菜单ID和排序值数量不匹配");
        }

        try {
            for (int i = 0; i < navIdArr.length; i++) {
                Integer sortValue = Integer.parseInt(sortArr[i]);
                Record record = new Record();
                record.set("nav_id", navIdArr[i]);
                record.set("sort", sortValue);
                record.set("update_time", DateUtils.getTime());
                record.set("update_by", getLoginName());
                Db.update("eh_wx_nav", "nav_id", record);
            }
            return AjaxResult.success("批量排序修改成功");
        } catch (NumberFormatException e) {
            return AjaxResult.error("排序值必须为数字");
        }
    }

    /**
     * 处理PDF文件列表
     */
    private void processPdfFileList(JSONObject params, Record record) {
        String pdfFileIds = params.getString("pdfFileIds");
        String source = params.getString("source");

        if (StringUtils.isNotEmpty(pdfFileIds)) {
            String[] fileIds = pdfFileIds.split(",");

            // 查询PDF文件信息
            List<Map<String, Object>> fileList = new ArrayList<>();
            for (String fileId : fileIds) {
                if (StringUtils.isNotEmpty(fileId.trim())) {
                    Record fileInfo = Db.findFirst("SELECT file_id, original_name, access_url, file_type FROM eh_file_info WHERE file_id = ?", fileId.trim());
                    if (fileInfo != null) {
                        Map<String, Object> fileMap = new HashMap<>();
                        fileMap.put("id", fileInfo.getStr("file_id"));
                        fileMap.put("name", fileInfo.getStr("original_name"));
                        fileMap.put("url", fileInfo.getStr("access_url"));
                        fileMap.put("type", "pdf");
                        fileList.add(fileMap);
                    }
                }
            }

            // 将文件列表转换为JSON字符串存储到pdf_file_list
            if (!fileList.isEmpty()) {
                JSONArray jsonArray = new JSONArray();
                for (Map<String, Object> file : fileList) {
                    JSONObject fileObj = new JSONObject();
                    fileObj.put("id", file.get("id"));
                    fileObj.put("name", file.get("name"));
                    fileObj.put("url", file.get("url"));
                    fileObj.put("type", file.get("type"));
                    jsonArray.add(fileObj);
                }
                record.set("pdf_file_list", jsonArray.toJSONString());

                // 设置第一个文件为主文件（向后兼容）
                Map<String, Object> firstFile = fileList.get(0);
                record.set("pdf_file", firstFile.get("name"));
                record.set("pdf_file_id", firstFile.get("id"));

                // 根据source类型处理url字段
                if ("indexNav".equals(source)) {
                    // indexNav：单选模式，必须设置url字段为PDF文件地址
                    record.set("url", firstFile.get("url"));
                } else if ("infoNav".equals(source)) {
                    // infoNav：多选模式，如果只有一个文件也设置url字段
                    if (fileList.size() == 1) {
                        record.set("url", firstFile.get("url"));
                    }
                }
            } else {
                record.set("pdf_file_list", "");
                record.set("pdf_file", "");
                record.set("pdf_file_id", "");
                record.set("url", "");
            }
        } else {
            record.set("pdf_file_list", "");
            record.set("pdf_file", "");
            record.set("pdf_file_id", "");
            record.set("url", "");
        }
    }


    /**
     * 获取PDF文件列表
     */
    @PostMapping("/getPdfFileList")
    @ResponseBody
    public AjaxResult getPdfFileList() {
        JSONObject params = getParams();
        String navId = params.getString("nav_id");

        if (StringUtils.isEmpty(navId)) {
            return AjaxResult.error("菜单ID不能为空");
        }

        Record navInfo = Db.findFirst("SELECT pdf_file_list FROM eh_wx_nav WHERE nav_id = ?", navId);
        if (navInfo == null) {
            return AjaxResult.error("菜单不存在");
        }

        String pdfFileList = navInfo.getStr("pdf_file_list");
        List<Map<String, Object>> fileList = new ArrayList<>();

        if (StringUtils.isNotEmpty(pdfFileList)) {
            try {
                JSONArray jsonArray = JSONArray.parseArray(pdfFileList);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject fileObj = jsonArray.getJSONObject(i);
                    Map<String, Object> fileMap = new HashMap<>();
                    String fileId = fileObj.getString("id");
                    fileMap.put("file_id", fileId);
                    fileMap.put("fileId", fileId);
                    fileMap.put("original_name", fileObj.getString("name"));
                    fileMap.put("fileName", fileObj.getString("name"));
                    fileMap.put("access_url", fileObj.getString("url"));
                    fileMap.put("fileUrl", fileObj.getString("url"));
                    fileMap.put("file_type", fileObj.getString("type"));
                    fileMap.put("fileType", fileObj.getString("type"));
                    fileList.add(fileMap);
                }
            } catch (Exception e) {
                logger.error("解析PDF文件列表失败: " + e.getMessage(),e);
            }
        }
        return AjaxResult.success(fileList);
    }

    /**
     * 验证菜单层级（不能超过二级）
     */
    private AjaxResult validateMenuLevel(int parentId) {
        if (parentId == 0) {
            return AjaxResult.success();
        }

        // 查询父菜单的parent_id
        Integer grandParentId = Db.queryInt("SELECT parent_id FROM eh_wx_nav WHERE nav_id = ?", parentId);
        if (grandParentId != null && grandParentId != 0) {
            return AjaxResult.error("不能创建三级菜单，请选择一级菜单作为父菜单");
        }

        return AjaxResult.success();
    }

    /**
     * 保存导航附件关联
     */
    private boolean saveNavAttachments(String businessType, String businessId, String[] fileIds, String createBy, String communityId) {
        try {
            // 删除现有关联
            Db.update("DELETE FROM eh_attachment_relation WHERE business_type = ? AND business_id = ?", businessType, businessId);

            // 添加新的关联
            if (fileIds != null && fileIds.length > 0) {
                for (int i = 0; i < fileIds.length; i++) {
                    Record record = new Record();
                    record.set("relation_id", Seq.getId());
                    record.set("business_type", businessType);
                    record.set("business_id", businessId);
                    record.set("file_id", fileIds[i]);
                    record.set("sort_order", i + 1);
                    record.set("create_time", DateUtils.getNowDate());
                    record.set("create_by", createBy);
                    record.set("community_id", communityId);
                    record.set("status", "0");

                    Db.save("eh_attachment_relation", "relation_id", record);

                    // 更新文件表的business_id
                    Db.update("UPDATE eh_file_info SET business_id = ? WHERE file_id = ?", businessId, fileIds[i]);
                }
            }
            return true;
        } catch (Exception e) {
            logger.error("保存导航附件关联失败", e);
            return false;
        }
    }

    /**
     * 构建树形结构数据
     */
    private List<Map<String, Object>> buildNavTree(List<Record> records) {
        List<Map<String, Object>> result = new ArrayList<>();
        Map<Integer, List<Map<String, Object>>> childrenMap = new HashMap<>();

        // 转换Record为Map并按parent_id分组
        for (Record record : records) {
            Map<String, Object> navMap = record.toMap();
            Integer parentId = record.getInt("parent_id");

            if (parentId == 0) {
                // 顶级菜单
                result.add(navMap);
            } else {
                // 子菜单
                childrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(navMap);
            }
        }

        // 为每个父菜单添加children
        addChildrenToParents(result, childrenMap);

        return result;
    }

    /**
     * 递归添加子菜单
     */
    private void addChildrenToParents(List<Map<String, Object>> parents, Map<Integer, List<Map<String, Object>>> childrenMap) {
        for (Map<String, Object> parent : parents) {
            Integer navId = (Integer) parent.get("nav_id");
            List<Map<String, Object>> children = childrenMap.get(navId);
            if (children != null && !children.isEmpty()) {
                parent.put("children", children);
                // 递归处理子菜单的子菜单（虽然我们限制了二级，但保持代码的通用性）
                addChildrenToParents(children, childrenMap);
            }
        }
    }

    /**
     * 获取菜单树数据（用于父菜单选择）
     */
    @GetMapping("/navTreeData")
    @ResponseBody
    public List<Map<String, Object>> navTreeData(@RequestParam(required = false) String source) {

        EasySQL sql = new EasySQL();
        sql.append("from eh_wx_nav t1");
        sql.append("where 1=1");
        sql.append(getSysUser().getCommunityId(),"and community_id = ?");
        sql.append(source,"and t1.source = ?");
        sql.append("and t1.parent_id = 0"); // 只返回一级菜单，用于父菜单选择
        sql.append("order by t1.sort, t1.update_time desc");

        List<Record> records = Db.find("select nav_id, nav_name, parent_id" + sql.toFullSql());

        List<Map<String, Object>> result = new ArrayList<>();
        for (Record record : records) {
            Map<String, Object> node = new HashMap<>();
            node.put("id", record.getInt("nav_id"));
            node.put("name", record.getStr("nav_name"));
            node.put("pId", 0);
            result.add(node);
        }

        return result;
    }

}
