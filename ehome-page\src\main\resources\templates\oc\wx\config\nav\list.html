<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:if="${source == 'indexNav'}" th:include="include :: header('首页导航菜单列表')" />
	<th:block th:if="${source == 'infoNav'}" th:include="include :: header('信息导航菜单列表')" />
	<th:block th:if="${source != 'indexNav' && source != 'infoNav'}" th:include="include :: header('小程序菜单列表')" />
</head>
<body class="gray-bg">
    <div class="container-div">
		<div class="row">
	        <div class="btn-group-sm" id="toolbar" role="group">
		        <a class="btn btn-success" onclick="$.operate.addFull()">
		            <i class="fa fa-plus"></i> 新增
		        </a>
		        <a class="btn btn-info" onclick="saveSort()">
		            <i class="fa fa-sort-amount-asc"></i> 保存排序
		        </a>
		        <a class="btn btn-default" id="expandAllBtn">
		            <i class="fa fa-exchange"></i> 展开/折叠
		        </a>
		        <a class="btn btn-warning" onclick="goToMenuClickLogs()">
		            <i class="fa fa-bar-chart"></i> 菜单点击日志
		        </a>
	        </div>
	        <div class="col-sm-12 select-table table-striped">
	            <table id="bootstrap-tree-table"></table>
	        </div>
    	</div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/wxNav";
        var source = /*[[${source}]]*/ '';
        var isAdmin = [[${isAdmin}]];
        var originalOrders = {}; // 存储原始排序值

        // nav_type中文映射
        var navTypeMap = {
            'text': '文本',
            'pdf': 'PDF',
            'url': '链接',
            'miniprogram': '小程序',
			'page': '内置页面'
        };

        // nav_type颜色映射
        var navTypeColorMap = {
            'text': 'label-info',      // 蓝色 - 文本
            'pdf': 'label-warning',    // 橙色 - PDF
            'url': 'label-success',    // 绿色 - 链接
            'miniprogram': 'label-primary', // 深蓝色 - 小程序
            'page': 'label-default'    // 灰色 - 内置页面
        };

        $(function() {
            var options = {
                code: "nav_id",
                parentCode: "parent_id",
                uniqueId: "nav_id",
                expandAll: true,
                expandFirst: true,
                url: prefix + "/list?source="+source,
                viewUrl: prefix + "/view/{id}",
                createUrl: prefix + "/add?source=" + source,
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove?ids={id}",
                modalName: source == 'indexNav' ? "首页导航菜单" : (source == 'infoNav' ? "信息导航菜单" : "小程序菜单"),
                queryParams: function(params) {
                    var search = $.table.queryParams(params);
                    search.source = source;
                    return search;
                },
                columns: [{
                    field: 'selectItem',
                    radio: true
                 },
				{
		            field: 'nav_name',
		            title: '菜单名称',
		            formatter: function (value, row, index) {
						// 构建图标显示
						var iconHtml = '';
						if (row.icon_name && row.icon_name.trim() !== '') {
							iconHtml = '<i class="' + row.icon_name + '"></i> ';
						}

						// 只有text、pdf、url类型才支持点击查看详情
						var supportedTypes = ['text', 'pdf', 'url'];
						var canView = supportedTypes.includes(row.nav_type);

						var nameHtml = iconHtml + '<span class="nav-label">' + value + '</span>';
						if(row.is_default!='0' && canView){
							nameHtml = '<a href="javascript:void(0)" onclick="$.operate.view(\'' + row.nav_id + '\')" title="点击查看详情">' +
								   iconHtml + '<span class="nav-label">' + value + '</span></a>';
						}

						return nameHtml;
		            }
		        },
				{
					field : 'sort',
					title : '排序',
					width: '10',
					widthUnit: '%',
					align: "center",
					formatter: function(value, row, index) {
						var navIdText = $.common.sprintf("<input type='hidden' name='navIds' value='%s'>", row.nav_id);
						var sortText = $.common.sprintf("<input type='text' name='sorts' value='%s' class='form-control' style='display: inline-block; width:60px; text-align:center;'>", value);
						originalOrders[row.nav_id] = value;
						return navIdText + sortText;
					}
				},
				{
		            field: 'is_default',
		            title: '菜单类型',
		            align: 'left',
		            formatter: function(value, row, index) {
		            	// 如果菜单有子菜单，不显示菜单类型
		            	if (row.hasChildren) {
		            		return '<span class="text-muted">父菜单</span>';
		            	}

		            	var navTypeText = navTypeMap[row.nav_type] || row.nav_type || '文本';
		            	var navTypeColor = navTypeColorMap[row.nav_type] || 'label-default';
		            	var typeText = '';
		            	var clickHandler = '';

		            	if(value=='0'){
							typeText = '<span class="label label-primary" data-type="'+row.nav_type+'">默认</span> ';
							typeText += '<span class="label ' + navTypeColor + '" data-type="'+row.nav_type+'">' + navTypeText + '</span>';
						}else{
							typeText = '<span class="label label-success" data-type="'+row.nav_type+'">自定义</span> ';
							typeText += '<span class="label ' + navTypeColor + '" data-type="'+row.nav_type+'">' + navTypeText + '</span>';
						}

						// 如果是超级管理员，添加点击修改功能
						if (isAdmin) {
							clickHandler = ' style="cursor: pointer;" onclick="changeIsDefault(\'' + row.nav_id + '\', \'' + value + '\')" title="点击修改默认类型"';
							typeText = typeText.replace('<span', '<span' + clickHandler);
						}

						return typeText;
		            }
		        },
				{
		            field: 'status',
		            title: '启用状态',
		            align: 'center',
		            formatter: function(value, row, index) {
		            	return statusTools(row);
		            }
		        },
				{
		            field: 'index_show',
		            title: '首页显示',
		            align: 'center',
		            visible: source === 'indexNav',  // 只有首页导航才显示首页显示列
		            formatter: function(value, row, index) {
		            	return indexShowTools(row);
		            }
		        },
				{
		            field: 'top_show',
		            title: '置顶显示',
		            align: 'center',
		            visible: source === 'indexNav',  // 只有首页导航才显示置顶列
		            formatter: function(value, row, index) {
		            	return topShowTools(row);
		            }
		        },
				{
		            field: 'remark',
		            title: '菜单描述',
		            align: 'left',
		            visible: source === 'indexNav',  // 只有首页导航才显示描述列
		            formatter: function(value, row, index) {
		            	return value || '<span class="text-muted">-</span>';
		            }
		        },
				{
					field : 'update_by',
					title : '创建者' 
				},
				{
		            field: 'create_time',
		            title: '创建时间',
		            sortable: true
		        },
		        {
		            title: '操作',
		            width: '20',
		            widthUnit: '%',
		            align: 'center',
		            formatter: function(value, row, index) {
		            	var actions = [];
						if(row.is_default=='0'){
							//actions.push('<a class="btn btn-primary btn-xs" href="javascript:void(0)" onclick="navConfPage(\'' + row.nav_id + '\',\'' + row.nav_code + '\')"><i class="fa fa-eye"></i>配置</a> ');
						}
						if(row.is_default!='0'){
							actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="$.operate.editFull(\'' + row.nav_id + '\')"><i class="fa fa-edit"></i>编辑</a> ');

							// 只有一级菜单才显示"新增"按钮（用于添加子菜单）
							if (!row.parent_id || row.parent_id == 0) {
								actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="addSubMenu(\'' + row.nav_id + '\')"><i class="fa fa-plus"></i>新增</a> ');
							}

							actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.nav_id + '\')"><i class="fa fa-remove"></i>删除</a>');
						}
						return actions.join('');
		            }
		        }]
            };
            $.treeTable.init(options);

            // 展开/折叠按钮事件
            $("#expandAllBtn").click(function() {
                var $icon = $(this).find('i');
                if ($icon.hasClass('fa-exchange')) {
                    $('#bootstrap-tree-table').bootstrapTreeTable('expandAll');
                    $icon.removeClass('fa-exchange').addClass('fa-compress');
                } else {
                    $('#bootstrap-tree-table').bootstrapTreeTable('collapseAll');
                    $icon.removeClass('fa-compress').addClass('fa-exchange');
                }
            });
        });

        // 添加子菜单
        function addSubMenu(parentId) {
            var url = prefix + "/add?source=" + source + "&parentId=" + parentId;
            $.modal.openFull("新增子菜单", url);
        }

        /* 保存排序-菜单 */
        function saveSort() {
            var changedNavIds = [];
            var changedSorts = [];
            $("input[name='navIds']").each(function() {
                var navId = $(this).val();
                var currentSort = $(this).next("input[name='sorts']").val();
                if (originalOrders[navId] !== currentSort) {
                    changedNavIds.push(navId);
                    changedSorts.push(currentSort);
                }
            });
            if (changedNavIds.length === 0) {
                $.modal.alertWarning("未检测到排序修改");
                return;
            }
            $.operate.post(prefix + "/updateBatchSort", { "navIds": changedNavIds.join(","), "sorts": changedSorts.join(",") });
        }

		function  navConfPage(id,code){
			var url = prefix + "/confNav";
			if(code==''){
				url = prefix + "/confNav?nav_id="+id;
			}
			$.modal.popupRight("配置", url);
		}

		/* 菜单状态显示 */
		function statusTools(row) {
		    if (row.status == 1) {
		    	return '<i class="fa fa-toggle-off text-info fa-2x" onclick="enable(\'' + row.nav_id + '\')"></i> ';
		    } else {
		    	return '<i class="fa fa-toggle-on text-info fa-2x" onclick="disable(\'' + row.nav_id + '\')"></i> ';
		    }
		}

		/* 首页显示状态 */
		function indexShowTools(row) {
		    if (row.index_show == 1) {
		    	return '<i class="fa fa-toggle-on text-info fa-2x" onclick="cancelIndexShow(\'' + row.nav_id + '\')" title="点击取消首页显示"></i> ';
		    } else {
		    	return '<i class="fa fa-toggle-off text-info fa-2x" onclick="setIndexShow(\'' + row.nav_id + '\')" title="点击设为首页显示"></i> ';
		    }
		}

		/* 置顶显示状态 */
		function topShowTools(row) {
		    if (row.top_show == 1) {
		    	return '<i class="fa fa-toggle-on text-info fa-2x" onclick="cancelTopShow(\'' + row.nav_id + '\')" title="点击取消置顶"></i> ';
		    } else {
		    	return '<i class="fa fa-toggle-off text-info fa-2x" onclick="setTopShow(\'' + row.nav_id + '\')" title="点击设为置顶"></i> ';
		    }
		}

		/* 菜单管理-停用 */
		function disable(navId) {
			$.modal.confirm("确认要停用菜单吗？", function() {
				$.operate.post(prefix + "/changeStatus", { "nav_id": navId, "status": 1 });
		    })
		}

		/* 菜单管理-启用 */
		function enable(navId) {
			$.modal.confirm("确认要启用菜单吗？", function() {
				$.operate.post(prefix + "/changeStatus", { "nav_id": navId, "status": 0 });
		    })
		}

		/* 设置首页显示 */
		function setIndexShow(navId) {
			$.operate.post(prefix + "/changeIndexShow", { "nav_id": navId, "index_show": 1 });
		}

		/* 取消首页显示 */
		function cancelIndexShow(navId) {
			$.operate.post(prefix + "/changeIndexShow", { "nav_id": navId, "index_show": 0 });
		}

		/* 设置置顶显示 */
		function setTopShow(navId) {
			// 检查当前置顶数量
			checkTopShowCount(function(count) {
				var message = "确认要设置该菜单为置顶显示吗？";
				if (count >= 2) {
					message += "<br><span style='color: #ff6b6b; font-size: 12px;'>⚠️ 当前已有" + count + "个置顶菜单，建议最多保持2个置顶菜单</span>";
				} else if (count === 0) {
					message += "<br><span style='color: #51cf66; font-size: 12px;'>💡 建议设置2个菜单为置顶显示</span>";
				} else if (count === 1) {
					message += "<br><span style='color: #51cf66; font-size: 12px;'>💡 再设置1个菜单为置顶，建议保持2个置顶菜单</span>";
				}

				$.modal.confirm(message, function() {
					$.operate.post(prefix + "/changeTopShow", { "nav_id": navId, "top_show": 1 });
			    })
			});
		}

		/* 取消置顶显示 */
		function cancelTopShow(navId) {
			// 检查当前置顶数量
			checkTopShowCount(function(count) {
				var message = "确认要取消该菜单的置顶显示吗？";
				if (count <= 2) {
					message += "<br><span style='color: #ff6b6b; font-size: 12px;'>⚠️ 当前只有" + count + "个置顶菜单，建议保持2个置顶菜单</span>";
				}

				$.modal.confirm(message, function() {
					$.operate.post(prefix + "/changeTopShow", { "nav_id": navId, "top_show": 0 });
			    })
			});
		}

		/* 检查置顶菜单数量 */
		function checkTopShowCount(callback) {
			$.ajax({
				url: prefix + "/list",
				type: "post",
				data: { source: source, pageNum: 1, pageSize: 1000 },
				success: function(result) {
					if (result.code == 0) {
						var count = 0;
						if (result.rows) {
							for (var i = 0; i < result.rows.length; i++) {
								if (result.rows[i].top_show == 1) {
									count++;
								}
							}
						}
						callback(count);
					} else {
						callback(0);
					}
				},
				error: function() {
					callback(0);
				}
			});
		}

		/* 菜单排序编辑 */
		function editSort(navId, currentSort) {
			layer.prompt({
				formType: 0,
				value: currentSort,
				title: '请输入新的排序值（数字越小越靠前）：'
			}, function(newSort, index) {
				if (newSort === null || newSort === '') {
					layer.msg('排序值不能为空', {icon: 5}); // 5表示失败图标
					return false;
				}
				if (!/^\d+$/.test(newSort)) {
					layer.msg('排序值必须为正整数', {icon: 5}); // 5表示失败图标
					return false;
				}
				layer.close(index);
				$.operate.post(prefix + "/updateSort", { "nav_id": navId, "sort": newSort });
			});
		}

		/* 菜单默认类型修改（仅超级管理员） */
		function changeIsDefault(navId, currentIsDefault) {
			if (!isAdmin) {
				$.modal.alertWarning('只有超级管理员才能修改菜单默认类型');
				return;
			}

			var newIsDefault = currentIsDefault == '0' ? '1' : '0';
			var actionText = newIsDefault == '0' ? '设为默认' : '设为自定义';

			$.modal.confirm("确认要" + actionText + "菜单吗？", function() {
				$.operate.post(prefix + "/changeIsDefault", { "nav_id": navId, "is_default": newIsDefault });
		    });
		}

		// 跳转到菜单点击日志页面
		function goToMenuClickLogs() {
			// 获取当前选中的菜单（树形表格）
			var selectedRows = $("#bootstrap-tree-table").bootstrapTreeTable('getSelections');

			console.log('[NavConfig] 选中的行数据:', selectedRows);

			if (selectedRows && selectedRows.length > 0) {
				// 如果选中了菜单，显示该菜单的点击日志
				var selectedRow = selectedRows[0];
				console.log('[NavConfig] 显示菜单点击日志:', selectedRow.nav_name);
				showMenuClickLogModal(selectedRow.nav_id, selectedRow.nav_name);
			} else {
				// 如果没有选中菜单，跳转到全部菜单点击日志页面
				console.log('[NavConfig] 跳转到菜单点击日志页面');
				window.open(ctx + 'system/notice/allMenuClickLogs', '_blank');
			}
		}

		// 显示指定菜单的点击日志弹窗
		function showMenuClickLogModal(menuId, menuName) {
			var url = ctx + 'system/notice/allMenuClickLogs?menuId=' + menuId + '&menuName=' + encodeURIComponent(menuName);

			$.modal.open("菜单点击日志 - " + menuName, url, '1200', '600');
		}
    </script>
</body>
</html>