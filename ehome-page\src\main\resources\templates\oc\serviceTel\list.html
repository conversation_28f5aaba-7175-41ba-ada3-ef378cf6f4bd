<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('小区电话本')" />
    <style>
        .btn-group .btn-outline-primary {
            border-color: #007bff;
            color: #007bff;
        }
        .btn-group .btn-outline-primary.active {
            background-color: #007bff;
            border-color: #007bff;
            color: #fff;
        }
        .btn-group .btn-outline-primary:hover {
            background-color: #007bff;
            border-color: #007bff;
            color: #fff;
        }
    </style>
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">                    
                    <div class="select-list" style="margin-bottom: 0px;">
                        <div class="row">
                            <div class="col-sm-12">
                                <label style="font-weight: bold; margin-right: 15px; font-size: 14px;">号码类型：</label>
                                <input type="hidden" name="tel_type" value="internal" id="telTypeInput">
                                <div class="btn-group" id="telTypeGroup">
                                    <button type="button" class="btn btn-outline-primary active" id="internalBtn" data-type="internal">
                                        物业号码
                                    </button>
                                    <button type="button" class="btn btn-outline-primary" id="externalBtn" data-type="external">
                                        公益号码
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 其他搜索条件 -->
                    <div class="select-list" style="padding: 15px 0px;">
                        <ul>
                            <li>
                                <label>电话号码：</label>
                                <input type="text" name="tel_number"/>
                            </li>
                            <li>
                                <label>服务名称：</label>
                                <input type="text" name="service_name"/>
                            </li>
                            <li>
                                <label>状态：</label>
                                <select name="status">
                                    <option value="">所有</option>
                                    <option value="0">正常</option>
                                    <option value="1">停用</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.treeTable.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="addServiceTel()">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-info" onclick="saveSort()">
                    <i class="fa fa-sort-amount-asc"></i> 保存排序
                </a>
                <a class="btn btn-warning" onclick="importServiceTel()">
                    <i class="fa fa-upload"></i> 导入号码
                </a>
                <a class="btn btn-default" id="expandAllBtn">
                    <i class="fa fa-exchange"></i> 展开/折叠
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-tree-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/serviceTel";
        var removeFlag = true;
        var editFlag = true;
        var originalOrders = {}; // 存储原始排序值

        $(function() {
            $('#formId').renderSelect({prefix:prefix});
            var options = {
                code: "service_tel_id",
                parentCode: "parent_id",
                uniqueId: "service_tel_id",
                expandAll: false,
                expandFirst: false,
                url: prefix + "/list",
                viewUrl: prefix + "/view/{id}",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove?ids={id}",
                modalName: "服务电话信息",
                layer:{
                    area:['800px','600px'],
                },
                queryParams: function(params) {
                    var search = $.table.queryParams(params);
                    // 添加当前选中的号码类型
                    search.tel_type = $('#telTypeInput').val();
                    return search;
                },
                columns: [{
                    field: 'selectItem',
                    radio: true
                },
                {
                    field: 'service_name',
                    title: '服务名称',
                    formatter: function(value, row, index) {
                        return value;
                    }
                },
                {
                    field: 'tel_number',
                    title: '电话号码',
                    formatter: function(value, row, index) {
                        if (row.parent_id == '0') {
                            return '-'; // 分类不显示电话号码
                        }
                        return value || '-';
                    }
                },
                {
                    field: 'company_name',
                    title: '公司名称'
                },

                {
                    field: 'sort_order',
                    title: '排序',
                    align: "center",
                    formatter: function(value, row, index) {
                        var serviceTelIdText = $.common.sprintf("<input type='hidden' name='serviceTelIds' value='%s'>", row.service_tel_id);
                        var sortText = $.common.sprintf("<input type='text' name='sortOrders' value='%s' class='form-control' style='display: inline-block; width:60px; text-align:center;'>", value);
                        originalOrders[row.service_tel_id] = value;
                        return serviceTelIdText + sortText;
                    }
                },
                {
                    field: 'status',
                    title: '状态',
                    formatter: function(value, row, index) {
                        if (value == '0') {
                            return '<span class="badge badge-primary">正常</span>';
                        } else if (value == '1') {
                            return '<span class="badge badge-danger">停用</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'create_time',
                    title: '创建时间'
                },
                {
                    title: '操作',
                    align: 'left',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.service_tel_id + '\')"><i class="fa fa-edit"></i>编辑</a> ');

                        // 一级分类显示新增子项按钮
                        if (row.parent_id == '0') {
                            actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="addSubServiceTel(\'' + row.service_tel_id + '\')"><i class="fa fa-plus"></i>子项</a> ');
                        }

                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.service_tel_id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.treeTable.init(options);

            // 展开/折叠按钮事件
            $("#expandAllBtn").click(function() {
                var $icon = $(this).find('i');
                if ($icon.hasClass('fa-exchange')) {
                    $('#bootstrap-tree-table').bootstrapTreeTable('expandAll');
                    $icon.removeClass('fa-exchange').addClass('fa-compress');
                } else {
                    $('#bootstrap-tree-table').bootstrapTreeTable('collapseAll');
                    $icon.removeClass('fa-compress').addClass('fa-exchange');
                }
            });

            // 号码类型切换事件 - 自动搜索
            $('#telTypeGroup button').on('click', function() {
                var telType = $(this).data('type');

                // 更新隐藏字段的值
                $('#telTypeInput').val(telType);

                // 更新按钮状态
                $('#telTypeGroup button').removeClass('active');
                $(this).addClass('active');

                // 直接触发搜索
                $.treeTable.search();
            });
        });

        // 添加服务电话
        function addServiceTel() {
            var telType = $('#telTypeInput').val();
            var url = prefix + "/add?tel_type=" + telType;
            var typeName = telType === 'internal' ? '内部号码' : '外部号码';
            $.modal.open("新增" + typeName, url);
        }

        // 添加子项
        function addSubServiceTel(parentId) {
            var telType = $('#telTypeInput').val();
            var url = prefix + "/add?parentId=" + parentId + "&tel_type=" + telType;
            $.modal.open("新增子项", url);
        }

        // 保存排序
        function saveSort() {
            var changedServiceTelIds = [];
            var changedSortOrders = [];
            $("input[name='serviceTelIds']").each(function() {
                var serviceTelId = $(this).val();
                var currentSort = $(this).next("input[name='sortOrders']").val();
                if (originalOrders[serviceTelId] !== currentSort) {
                    changedServiceTelIds.push(serviceTelId);
                    changedSortOrders.push(currentSort);
                }
            });
            if (changedServiceTelIds.length === 0) {
                $.modal.alertWarning("未检测到排序修改");
                return;
            }
            $.operate.post(prefix + "/updateBatchSort", {
                "serviceTelIds": changedServiceTelIds.join(","),
                "sortOrders": changedSortOrders.join(",")
            });
        }

        // 导入服务电话
        function importServiceTel() {
            var telType = $('#telTypeInput').val();
            // 首先获取一级分类列表
            $.get(prefix + "/getTopCategories", {tel_type: telType}, function(result) {
                if (result.code !== 0) {
                    layer.msg('获取分类列表失败', {icon: 5});
                    return;
                }

                var categories = result.data || [];
                if (categories.length === 0) {
                    var typeName = telType === 'internal' ? '内部号码' : '外部号码';
                    layer.msg('请先创建' + typeName + '的一级分类', {icon: 5});
                    return;
                }

                // 显示分类选择和数据输入界面
                showImportDialog(categories);
            });
        }

        // 显示导入对话框
        function showImportDialog(categories) {
            var categoryOptions = '';
            for (var i = 0; i < categories.length; i++) {
                categoryOptions += '<option value="' + categories[i].service_tel_id + '">' + categories[i].service_name + '</option>';
            }

            var html = '<div style="padding: 10px;">';
            html += '<div class="form-group">';
            html += '<label>选择导入到的分类：</label>';
            html += '<select id="importCategory" class="form-control" style="margin-bottom: 15px;width: 95%;">';
            html += categoryOptions;
            html += '</select>';
            html += '</div>';
            html += '<div class="form-group">';
            html += '<label>粘贴服务电话数据：</label>';
            html += '<textarea style="width:95%;" id="importData" class="form-control" rows="8" placeholder="请粘贴服务电话数据，格式如下：&#10;报警电话    110&#10;救护车    119&#10;消防电话    120&#10;&#10;每行一个，服务名称和电话号码用空格或制表符分隔"></textarea>';
            html += '</div>';
            html += '</div>';

            layer.open({
                type: 1,
                title: '导入服务电话',
                area: ['500px', '420px'],
                content: html,
                scrollbar:false,
                btn: ['解析数据', '取消'],
                yes: function(index, layero) {
                    var categoryId = layero.find('#importCategory').val();
                    var categoryName = layero.find('#importCategory option:selected').text();
                    var dataText = layero.find('#importData').val();

                    if (!dataText || dataText.trim() === '') {
                        layer.msg('请输入要导入的数据', {icon: 5});
                        return false;
                    }

                    // 解析数据
                    var importData = parseImportData(dataText);
                    if (importData.length === 0) {
                        layer.msg('没有解析到有效数据，请检查格式', {icon: 5});
                        return false;
                    }

                    layer.close(index);

                    // 显示确认对话框
                    showImportConfirm(importData, categoryId, categoryName);
                }
            });
        }

        // 解析导入数据
        function parseImportData(text) {
            var lines = text.split('\n');
            var data = [];

            for (var i = 0; i < lines.length; i++) {
                var line = lines[i].trim();
                if (line === '') continue;

                // 用空格或制表符分割
                var parts = line.split(/\s+/);
                if (parts.length >= 2) {
                    var serviceName = parts[0].trim();
                    var telNumber = parts[parts.length - 1].trim(); // 取最后一部分作为电话号码

                    // 如果有多个部分，中间的部分也加入服务名称
                    if (parts.length > 2) {
                        serviceName = parts.slice(0, -1).join(' ').trim();
                    }

                    if (serviceName && telNumber) {
                        data.push({
                            serviceName: serviceName,
                            telNumber: telNumber
                        });
                    }
                }
            }

            return data;
        }

        // 显示导入确认对话框
        function showImportConfirm(importData, categoryId, categoryName) {
            var html = '<div style="max-height: 400px; overflow-y: auto;">';
            html += '<p>解析到 <strong>' + importData.length + '</strong> 条数据，将导入到分类 <strong>' + categoryName + '</strong> 下：</p>';
            html += '<table class="table table-bordered table-striped" style="margin-bottom: 10px;">';
            html += '<thead><tr><th>服务名称</th><th>电话号码</th></tr></thead>';
            html += '<tbody>';

            for (var i = 0; i < importData.length; i++) {
                html += '<tr>';
                html += '<td>' + importData[i].serviceName + '</td>';
                html += '<td>' + importData[i].telNumber + '</td>';
                html += '</tr>';
            }

            html += '</tbody></table>';
            html += '<p class="text-muted">注意：重复的电话号码将被跳过</p>';
            html += '</div>';

            layer.confirm(html, {
                title: '确认导入数据',
                area: ['600px', '500px'],
                btn: ['确认导入', '取消']
            }, function(index) {
                // 执行导入
                doImport(importData, categoryId);
                layer.close(index);
            });
        }

        // 执行导入
        function doImport(importData, categoryId) {
            var requestData = {
                importData: importData,
                parentId: categoryId
            };

            $.ajax({
                url: prefix + "/batchImport",
                type: "POST",
                contentType: "application/json",
                data: JSON.stringify(requestData),
                success: function(result) {
                    if (result.code === 0) {
                        layer.msg(result.msg || '导入成功', {icon: 1});
                        // 刷新表格
                        $.treeTable.refresh();
                    } else {
                        layer.msg(result.msg || '导入失败', {icon: 5});
                    }
                },
                error: function() {
                    layer.msg('导入失败，请重试', {icon: 5});
                }
            });
        }
    </script>
</body>
</html>
